// src/components/toolbar/hooks/use-toolbar-state.ts
// Unified state management hook for toolbar components

import { useQueryState } from 'nuqs'
import { parseAsStringEnum } from 'nuqs/server'
import { generateId } from '@/lib/id'
import { FilterState, SortingState } from '../types'
import { useToolbar } from '../providers/toolbar-provider'
import { getFiltersStateParser } from '../utils/filter-parser'
import { getSearchStateParser } from '../utils/search-parser'
import { getSortingStateParser } from '../utils/sorting-lib'
import { getDefaultFilterOperator } from '../utils/filter-lib'

// Search state type
interface SearchState {
  id: string
  value: string
}

/**
 * Unified state management hook for all toolbar features.
 * Centralizes filter, search, and sorting state management with URL synchronization.
 * 
 * @returns Object containing state values, computed properties, and action functions
 */
export function useToolbarState() {
  const { startTransition } = useToolbar()
  
  // Filter state management
  const [filters, setFilters] = useQueryState(
    'filters',
    getFiltersStateParser().withDefault([]).withOptions({
      startTransition,
      clearOnDefault: true,
      shallow: false,
    })
  )

  // Join operator for combining multiple filters
  const [join, setJoin] = useQueryState(
    'join',
    parseAsStringEnum(['AND', 'OR']).withDefault('AND').withOptions({
      startTransition,
      clearOnDefault: true,
      shallow: false,
    })
  )

  // Search state management
  const [search, setSearch] = useQueryState(
    'search',
    getSearchStateParser().withDefault({ id: '', value: '' }).withOptions({
      startTransition,
      clearOnDefault: true,
      shallow: false,
    })
  )

  // Sorting state management
  const [sorting, setSorting] = useQueryState(
    'sort',
    getSortingStateParser().withDefault([]).withOptions({
      startTransition,
      clearOnDefault: true,
      shallow: false,
    })
  )

  // Computed values for UI state
  const hasActiveFilters = filters.length > 0
  const hasActiveSearch = search.value.length > 0
  const hasActiveSorting = sorting.length > 0
  const hasAnyActive = hasActiveFilters || hasActiveSearch || hasActiveSorting

  // Global reset actions
  const resetAll = () => {
    setFilters([])
    setSearch({ id: '', value: '' })
    setSorting([])
    setJoin('AND')
  }

  const resetFilters = () => {
    setFilters([])
    setJoin('AND')
  }

  const resetSearch = () => {
    setSearch({ id: '', value: '' })
  }

  const resetSorting = () => {
    setSorting([])
  }

  // Filter management actions
  const addFilter = (filter: Omit<FilterState, 'filterId'>) => {
    const newFilter: FilterState = { 
      ...filter, 
      filterId: generateId(),
      operator: filter.operator || getDefaultFilterOperator(filter.variant as any)
    }
    setFilters(prev => [...prev, newFilter])
  }

  const updateFilter = (filterId: string, updates: Partial<FilterState>) => {
    setFilters(prev => 
      prev.map(filter => 
        filter.filterId === filterId 
          ? { ...filter, ...updates }
          : filter
      )
    )
  }

  const removeFilter = (filterId: string) => {
    setFilters(prev => prev.filter(filter => filter.filterId !== filterId))
  }

  const reorderFilters = (newOrder: string[]) => {
    const filterMap = new Map(filters.map(filter => [filter.filterId, filter]))
    const reorderedFilters = newOrder
      .map(id => filterMap.get(id))
      .filter((filter): filter is FilterState => filter !== undefined)
    setFilters(reorderedFilters)
  }

  // Search management actions
  const updateSearch = (searchItem: SearchState) => {
    setSearch(searchItem)
  }

  // Sorting management actions
  const addSorting = (sort: SortingState) => {
    setSorting(prev => [...prev, sort])
  }

  const updateSorting = (id: string, updates: Partial<SortingState>) => {
    setSorting(prev =>
      prev.map(sort =>
        sort.id === id
          ? { ...sort, ...updates }
          : sort
      )
    )
  }

  const removeSorting = (id: string) => {
    setSorting(prev => prev.filter(sort => sort.id !== id))
  }

  const reorderSorting = (newOrder: string[]) => {
    const sortMap = new Map(sorting.map(sort => [sort.id, sort]))
    const reorderedSorting = newOrder
      .map(id => sortMap.get(id))
      .filter((sort): sort is SortingState => sort !== undefined)
    setSorting(reorderedSorting)
  }

  // Toggle sorting direction for existing sort or add new sort
  const toggleSorting = (id: string) => {
    const existingSort = sorting.find(sort => sort.id === id)
    if (existingSort) {
      updateSorting(id, { 
        value: existingSort.value === 'asc' ? 'desc' : 'asc' 
      })
    } else {
      addSorting({ id, value: 'asc' })
    }
  }

  return {
    // State values
    filters,
    join,
    search,
    sorting,
    
    // Computed properties
    hasActiveFilters,
    hasActiveSearch,
    hasActiveSorting,
    hasAnyActive,
    
    // Global actions
    resetAll,
    resetFilters,
    resetSearch,
    resetSorting,
    
    // Filter actions
    addFilter,
    updateFilter,
    removeFilter,
    reorderFilters,
    setJoin,
    
    // Search actions
    updateSearch,
    setSearch,
    
    // Sorting actions
    addSorting,
    updateSorting,
    removeSorting,
    reorderSorting,
    toggleSorting,
  }
}

/**
 * Hook for components that only need filter state management.
 * Provides a subset of the full toolbar state focused on filtering.
 */
export function useFilterState() {
  const {
    filters,
    join,
    hasActiveFilters,
    resetFilters,
    addFilter,
    updateFilter,
    removeFilter,
    reorderFilters,
    setJoin,
  } = useToolbarState()

  return {
    filters,
    join,
    hasActiveFilters,
    resetFilters,
    addFilter,
    updateFilter,
    removeFilter,
    reorderFilters,
    setJoin,
  }
}

/**
 * Hook for components that only need search state management.
 * Provides a subset of the full toolbar state focused on searching.
 */
export function useSearchState() {
  const {
    search,
    hasActiveSearch,
    resetSearch,
    updateSearch,
    setSearch,
  } = useToolbarState()

  return {
    search,
    hasActiveSearch,
    resetSearch,
    updateSearch,
    setSearch,
  }
}

/**
 * Hook for components that only need sorting state management.
 * Provides a subset of the full toolbar state focused on sorting.
 */
export function useSortingState() {
  const {
    sorting,
    hasActiveSorting,
    resetSorting,
    addSorting,
    updateSorting,
    removeSorting,
    reorderSorting,
    toggleSorting,
  } = useToolbarState()

  return {
    sorting,
    hasActiveSorting,
    resetSorting,
    addSorting,
    updateSorting,
    removeSorting,
    reorderSorting,
    toggleSorting,
  }
}
