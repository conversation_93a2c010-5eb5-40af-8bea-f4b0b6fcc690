import { createParser } from "nuqs/server"

import { SearchState, searchStateSchema } from "./search-lib"

export const getSearchStateParser = (itemKey?: string) => {
  return createParser({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value)
        const result = searchStateSchema.safeParse(parsed)

        if (!result.success) return null

        if (itemKey && result.data?.id !== itemKey) {
          return null
        }

        return result.data
      } catch {
        return null
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) => a.id === b.id && a.value === b.value,
  })
}

export const searchQueryParser = (search: SearchState) => {
  if (!search.id || !search.value) return {} // no search value or id provided, return null to skip search query parsing.
  return { [search.id]: { contains: search.value, mode: "insensitive" } }
}

// Legacy exports for backward compatibility during transition
export { getSearchStateParser as _getSearchStateParser }
export { searchQueryParser as _searchQueryParser }
