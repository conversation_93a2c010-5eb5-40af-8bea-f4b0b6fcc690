import * as React from "react"

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { useFilter } from "../providers/filter-provider"
import { useToolbar } from "../providers/toolbar-provider"
import { FilterState, getFilterOperators } from "../utils/filter-lib"

/**
 * مكون لاختيار كيفية تطبيق الفلتر على العنصر
 */
export function FilterOperatorSelector({
  filter,
  filterItemId,
}: {
  filter: FilterState
  filterItemId: string
}) {
  const { dictionaries, dir } = useToolbar()
  const { onFilterUpdate } = useFilter()

  const dict = dictionaries.filter.filterItem.operatorSelector
  const operatorListboxId = `${filterItemId}-operator-listbox`
  const filterOperators = getFilterOperators(filter.variant, dict.operators)

  type FilterOperator = (typeof filterOperators)[number]["value"]

  const onOperatorChange = React.useCallback(
    (operator: FilterOperator) => {
      if (filter.variant === "number" || filter.variant === "numberRange") {
        if (operator === "isBetween") {
          onFilterUpdate(filter.filterId, {
            operator: "isBetween",
            variant: "numberRange",
            value: ["", ""],
          })
        } else {
          onFilterUpdate(filter.filterId, {
            operator: operator,
            variant: "number",
            value: Array.isArray(filter.value) ? filter.value[0] : filter.value,
          })
        }
        return
      }

      if (filter.variant === "date" || filter.variant === "dateRange") {
        if (operator === "isBetween") {
          onFilterUpdate(filter.filterId, {
            operator: "isBetween",
            variant: "dateRange",
            value: ["", ""],
          })
        } else {
          onFilterUpdate(filter.filterId, {
            operator: operator,
            variant: "date",
            value: Array.isArray(filter.value) ? filter.value[0] : filter.value,
          })
        }
        return
      }

      if (operator === "isEmpty" || operator === "isNotEmpty") {
        onFilterUpdate(filter.filterId, {
          operator: operator,
          value: operator === "isEmpty" ? "empty" : "notEmpty",
        })
        return
      }

      onFilterUpdate(filter.filterId, {
        operator: operator,
      })
    },
    [filter.filterId, filter.value, filter.variant, onFilterUpdate]
  )

  return (
    <Select dir={dir} value={filter.operator} onValueChange={onOperatorChange}>
      <SelectTrigger
        aria-controls={operatorListboxId}
        className="h-8 w-32 rounded lowercase [&[data-size]]:h-8"
      >
        <div className="truncate">
          <SelectValue placeholder={filter.operator} />
        </div>
      </SelectTrigger>
      <SelectContent
        id={operatorListboxId}
        className="origin-[var(--radix-select-content-transform-origin)]"
      >
        {filterOperators.map((operator) => (
          <SelectItem
            key={operator.value}
            value={operator.value}
            className="lowercase"
          >
            {operator.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
