import * as React from "react"
import { GripVertical, Trash2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { SortableItem, SortableItemHandle } from "@/components/ui/sortable"

import { useFilter } from "../providers/filter-provider"
import { useToolbar } from "../providers/toolbar-provider"
import { FilterState } from "../utils/filter-lib"
import { FilterJoinSelector } from "./filter-item-join-selector"
import { FilterItemSelector } from "./filter-item-selector"
import { FilterValueFilter } from "./filter-item-value-filter"
import { FilterOperatorSelector } from "./filter-operator-selector"

export function FilterItem({
  index,
  filter,
  filterItemId,
}: {
  index: number
  filterItemId: string
  filter: FilterState
}) {
  const { dir } = useToolbar()
  const { items, onFilterRemove } = useFilter()

  const item = items.find((item) => item.id === filter.id)
  if (!item) return null

  return (
    <SortableItem dir={dir} value={filter.filterId} asChild>
      <div
        role="listitem"
        id={filterItemId}
        tabIndex={-1}
        className="flex items-center gap-2"
      >
        <FilterJoinSelector index={index} filterItemId={filterItemId} />
        <FilterItemSelector
          filterItemId={filterItemId}
          filter={filter}
          item={item}
        />
        <FilterOperatorSelector filter={filter} filterItemId={filterItemId} />
        <div className="min-w-36 flex-1">
          <FilterValueFilter
            filterItemId={filterItemId}
            filter={filter}
            item={item}
          />
        </div>
        <Button
          aria-controls={filterItemId}
          variant="outline"
          size="icon"
          className="size-8 rounded"
          onClick={() => onFilterRemove(filter.filterId)}
        >
          <Trash2 />
        </Button>
        <SortableItemHandle asChild>
          <Button variant="outline" size="icon" className="size-8 rounded">
            <GripVertical />
          </Button>
        </SortableItemHandle>
      </div>
    </SortableItem>
  )
}
