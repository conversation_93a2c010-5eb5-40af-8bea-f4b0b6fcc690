import { DynamicObject } from "@/types/globals"

import { SortingState } from "../utils/sorting-lib"

export type SortDirection = "asc" | "desc"

type SortingItem<T extends DynamicObject = DynamicObject> = {
  /** مفتاح كائن البيانات الذي تريد تمكين استخدامه للفرز */
  id: Extract<keyof T, string>
  /** العنوان الذي سيظهر في القائمة */
  label: string
}

export type OnSortRemove = (sortId: string) => void
export type OnSortUpdate = (
  sortId: string,
  updates: Partial<SortingState>
) => void

export interface SortingItemProps {
  sort: SortingState
  sortItemId: string
  items: { id: string; label: string }[]
  onSortUpdate: OnSortUpdate
  onSortRemove: OnSortRemove
}

export type SortingProps<T extends DynamicObject = DynamicObject> = {
  /**
   * مصفوفة العناصر التي تريد تمكين استخدامها للفرز.
   */
  items: SortingItem<T>[]
  className?: string
}

// Legacy exports for backward compatibility during transition
export type { SortDirection as _SortDirection }
export type { OnSortRemove as _OnSortRemove }
export type { OnSortUpdate as _OnSortUpdate }
export type { SortingItemProps as _SortingItemProps }
export type { SortingProps as _SortingProps }
