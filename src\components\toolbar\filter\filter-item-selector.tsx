import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

import { useFilter } from "../providers/filter-provider"
import { useToolbar } from "../providers/toolbar-provider"
import { FilterItemDef } from "../types/filter"
import { FilterState, getDefaultFilterOperator } from "../utils/filter-lib"

/**
 * مكون لاختيار العنصر الذي تريد تطبيق الفلتر عليه
 */
export function FilterItemSelector({
  filterItemId,
  filter,
  item,
}: {
  filter: FilterState
  filterItemId: string
  item: FilterItemDef
}) {
  const { dictionaries, dir } = useToolbar()
  const { items, onFilterUpdate } = useFilter()
  const [open, setOpen] = React.useState(false)

  const dict = dictionaries.filter.filterItem.itemSelector
  const fieldListboxId = `${filterItemId}-field-listbox`

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          role="combobox"
          aria-controls={fieldListboxId}
          variant="outline"
          size="sm"
          className="w-32 justify-between rounded font-normal"
        >
          <span className="truncate">{item.label}</span>
          <ChevronsUpDown className="opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        id={fieldListboxId}
        align="start"
        className="w-40 origin-[var(--radix-popover-content-transform-origin)] p-0"
      >
        <Command dir={dir}>
          <CommandInput placeholder={dict.searchPlaceholder} />
          <CommandList>
            <CommandEmpty>{dict.noFieldsFound}</CommandEmpty>
            <CommandGroup>
              {items.map((item) => (
                <CommandItem
                  key={item.id}
                  value={item.id}
                  onSelect={(value) => {
                    onFilterUpdate(filter.filterId, {
                      id: value,
                      variant: item.variant,
                      operator: getDefaultFilterOperator(item.variant),
                      value: "",
                    })

                    setOpen(false)
                  }}
                >
                  <span className="truncate">{item.label}</span>
                  <Check
                    className={cn(
                      "ms-auto",
                      item.id === filter.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
