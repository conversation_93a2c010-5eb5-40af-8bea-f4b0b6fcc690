import React, { useMemo } from "react"

import { Locale } from "@/types/globals"

import { getToolbarDictionary } from "./utils/toolbar-lib"
import { ToolbarProvider } from "./providers/toolbar-provider"

export interface ToolbarProps {
  className?: string
  children?: React.ReactNode

  startTransition?: React.TransitionStartFunction
  dir?: "rtl" | "ltr"
  locale?: Locale
}

function Toolbar({
  children,
  className,
  locale = "ar",
  startTransition,
  dir = "rtl",
}: ToolbarProps) {
  const dictionaries = useMemo(() => getToolbarDictionary(locale), [locale])

  return (
    <ToolbarProvider value={{ startTransition, dictionaries, dir }}>
      <div className={className} dir={dir}>
        {children}
      </div>
    </ToolbarProvider>
  )
}

export { Toolbar }
