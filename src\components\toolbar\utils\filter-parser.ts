import * as React from "react"
import { endOfDay, startOfDay } from "date-fns"
import { createParser } from "nuqs/server"
import { z } from "zod/v4"

import {
  FilterJoinState,
  FilterState,
  filterStateSchema,
} from "./filter-lib"

export const getFiltersStateParser = (itemIds?: string[] | Set<string>) => {
  const validKeys = itemIds
    ? itemIds instanceof Set
      ? itemIds
      : new Set(itemIds)
    : null

  return createParser({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value)
        const result = z.array(filterStateSchema).safeParse(parsed)

        if (!result.success) return null

        if (validKeys && result.data.some((item) => !validKeys.has(item.id))) {
          return null
        }

        return result.data
      } catch {
        return null
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) =>
      a.length === b.length &&
      a.every(
        (filter, index) =>
          filter.id === b[index]?.id &&
          filter.value === b[index]?.value &&
          filter.variant === b[index]?.variant &&
          filter.operator === b[index]?.operator
      ),
  })
}

/**
 * parser to prisam query whare query
 */
const textVariantParser = (filter: FilterState & { variant: "text" }) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "iLike": {
      return { [id]: { contains: value, mode: "insensitive" } }
    }
    case "notILike": {
      return { NOT: { [id]: { contains: value, mode: "insensitive" } } }
    }
    case "eq": {
      return { [id]: value }
    }
    case "ne": {
      return { NOT: { [id]: value } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

const numericVariantParser = (filter: FilterState & { variant: "number" }) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "eq": {
      return { [id]: Number(value) }
    }
    case "ne": {
      return { NOT: { [id]: Number(value) } }
    }
    case "lt": {
      return { [id]: { lt: Number(value) } }
    }
    case "lte": {
      return { [id]: { lte: Number(value) } }
    }
    case "gt": {
      return { [id]: { gt: Number(value) } }
    }
    case "gte": {
      return { [id]: { gte: Number(value) } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

const numericRangeVariantParser = (
  filter: FilterState & { variant: "numberRange" }
) => {
  const id = filter.id
  const value = filter.value

  return { [id]: { gt: Number(value[0]), lte: Number(value[1]) } }
}

const dateVariantParser = (filter: FilterState & { variant: "date" }) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator
  const date = new Date(Number(value))
  const startDay = startOfDay(date)
  const endDay = endOfDay(date)

  switch (operator) {
    case "eq": {
      return { [id]: { gte: startDay, lte: endDay } }
    }
    case "ne": {
      return { NOT: { [id]: { gte: startDay, lte: endDay } } }
    }
    case "lt": {
      return { [id]: { lt: startDay } }
    }
    case "lte": {
      return { [id]: { lte: endDay } }
    }
    case "gt": {
      return { [id]: { gt: endDay } }
    }
    case "gte": {
      return { [id]: { gte: startDay } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

const dateRangeVariantParser = (
  filter: FilterState & { variant: "dateRange" }
) => {
  const id = filter.id
  const value = filter.value
  const min = startOfDay(new Date(Number(value[0])))
  const max = endOfDay(new Date(Number(value[1])))

  return {
    [id]: { gte: min, lte: max },
  }
}

const booleanVariantParser = (
  filter: FilterState & { variant: "boolean" }
) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "eq": {
      return { [id]: Boolean(value) }
    }
    case "ne": {
      return { NOT: { [id]: Boolean(value) } }
    }
    default:
      return undefined
  }
}

const selectVariantParser = (filter: FilterState & { variant: "select" }) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "eq": {
      return { [id]: value }
    }
    case "ne": {
      return { NOT: { [id]: value } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

const multiSelectVariantParser = (
  filter: FilterState & { variant: "multiSelect" }
) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "inArray": {
      return { [id]: { in: value } }
    }
    case "notInArray": {
      return { NOT: { [id]: { in: value } } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

export const getFilterQueryParser = React.cache(
  ({ filters, join }: { filters: FilterState[]; join: FilterJoinState }) => {
    const parsedFilters = filters
      .map((filter) => {
        switch (filter.variant) {
          case "text":
            return textVariantParser(filter)
          case "number":
            return numericVariantParser(filter)
          case "numberRange":
            return numericRangeVariantParser(filter)
          case "date":
            return dateVariantParser(filter)
          case "dateRange":
            return dateRangeVariantParser(filter)
          case "boolean":
            return booleanVariantParser(filter)
          case "select":
            return selectVariantParser(filter)
          case "multiSelect":
            return multiSelectVariantParser(filter)
          default:
            return undefined
        }
      })
      .filter(Boolean)

    return parsedFilters.length > 0 ? { [join]: parsedFilters } : {}
  }
)

// Legacy exports for backward compatibility during transition
export { getFiltersStateParser as _getFiltersStateParser }
export { getFilterQueryParser as _getFilterQueryParser }
