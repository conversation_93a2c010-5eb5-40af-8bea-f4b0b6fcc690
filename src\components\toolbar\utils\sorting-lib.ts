import { createParser } from "nuqs/server"
import { z } from "zod/v4"

const sortingOrders = ["asc", "desc"] as const

const sortingStateSchema = z.object({
  id: z.string().min(1),
  value: z.enum(["asc", "desc"]),
})

type SortingState = z.infer<typeof sortingStateSchema>

const getSortingStateParser = (itemIds?: string[] | Set<string>) => {
  const validKeys = itemIds
    ? itemIds instanceof Set
      ? itemIds
      : new Set(itemIds)
    : null

  return createParser({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value)
        const result = z.array(sortingStateSchema).safeParse(parsed)

        if (!result.success) return null

        if (validKeys && result.data.some((item) => !validKeys.has(item.id))) {
          return null
        }

        return result.data
      } catch {
        return null
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) =>
      a.length === b.length &&
      a.every(
        (sort, index) =>
          sort.id === b[index]?.id && sort.value === b[index]?.value
      ),
  })
}

const getSortingQueryParser = (sorting: SortingState[]) => {
  const parsedSorting = sorting.map((sort) => ({
    [sort.id]: sort.value,
  }))

  return parsedSorting
}

export {
  sortingOrders,
  sortingStateSchema,
  type SortingState,
  getSortingStateParser,
  getSortingQueryParser,
  // Legacy exports for backward compatibility during transition
  sortingOrders as _sortingOrders,
  sortingStateSchema as _sortingStateSchema,
  type SortingState as _SortingState,
  getSortingStateParser as _getSortingStateParser,
  getSortingQueryParser as _getSortingQueryParser,
}
