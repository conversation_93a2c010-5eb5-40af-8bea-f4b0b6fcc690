import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { useFilter } from "../providers/filter-provider"
import { useToolbar } from "../providers/toolbar-provider"
import { FilterJoinState } from "../utils/filter-lib"

/**
 * مكون لاختيار كيفية ترابط الفلتر مع الفلتر السابق
 */
export function FilterJoinSelector({
  index,
  filterItemId,
}: {
  index: number
  filterItemId: string
}) {
  const { dictionaries, dir } = useToolbar()
  const { joinOperator, setJoinOperator } = useFilter()

  const dict = dictionaries.filter.filterItem.JoinSelector
  const joinOperatorListboxId = `${filterItemId}-join-operator-listbox`

  return (
    <div className="w-19 text-center">
      {index === 0 ? (
        <span className="text-muted-foreground text-sm">{dict.where}</span>
      ) : index === 1 ? (
        <Select
          dir={dir}
          value={joinOperator}
          onValueChange={(value: FilterJoinState) => setJoinOperator(value)}
        >
          <SelectTrigger
            aria-label="Select join operator"
            aria-controls={joinOperatorListboxId}
            className="h-8 w-full rounded lowercase [&[data-size]]:h-8"
          >
            <SelectValue placeholder={dict?.[joinOperator]} />
          </SelectTrigger>
          <SelectContent
            id={joinOperatorListboxId}
            position="popper"
            className="min-w-(--radix-select-trigger-width) lowercase"
          >
            <SelectItem value={"AND"}>{dict.AND}</SelectItem>
            <SelectItem value={"OR"}>{dict.OR}</SelectItem>
          </SelectContent>
        </Select>
      ) : (
        <span className="text-muted-foreground w-full text-sm lowercase">
          {dict?.[joinOperator]}
        </span>
      )}
    </div>
  )
}
