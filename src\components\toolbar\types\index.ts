// src/components/toolbar/types/index.ts
// Centralized type definitions for the toolbar component system

import React from 'react'
import { DynamicObject, Locale } from '@/types/globals'

// Base types
export type Direction = 'rtl' | 'ltr'
export type FilterVariant = 'text' | 'number' | 'numberRange' | 'date' | 'dateRange' | 'boolean' | 'select' | 'multiSelect'
export type SortDirection = 'asc' | 'desc'

// Operator types
export type TextOperator = 'iLike' | 'notILike' | 'eq' | 'ne' | 'isEmpty' | 'isNotEmpty'
export type NumberOperator = 'eq' | 'ne' | 'lt' | 'lte' | 'gt' | 'gte' | 'isBetween' | 'isEmpty' | 'isNotEmpty'
export type DateOperator = NumberOperator // Same operators as number
export type BooleanOperator = 'eq' | 'ne'
export type SelectOperator = 'eq' | 'ne' | 'isEmpty' | 'isNotEmpty'
export type MultiSelectOperator = 'inArray' | 'notInArray' | 'isEmpty' | 'isNotEmpty'

// Main component props
export interface ToolbarProps {
  className?: string
  children?: React.ReactNode
  startTransition?: React.TransitionStartFunction
  dir?: Direction
  locale?: Locale
}

// Filter types
export interface BaseFilterItem {
  /**
   * Unique identifier for the filter field.
   * Must match a property key in the data object being filtered.
   */
  id: string
  
  /**
   * Display label shown to users in the filter interface.
   * Should be localized based on the current locale (Arabic/English).
   */
  label: string
  
  /**
   * Placeholder text shown in empty input fields.
   * Should be localized based on the current locale.
   */
  placeholder?: string
}

export interface TextFilterItem extends BaseFilterItem {
  /**
   * Type of filter to render and validation to apply.
   * 'text': String-based filtering with operators like "contains", "equals"
   */
  variant: 'text'
}

export interface NumberFilterItem extends BaseFilterItem {
  /**
   * Type of filter to render and validation to apply.
   * 'number': Numeric filtering with operators like "greater than", "less than"
   */
  variant: 'number'
  
  /**
   * Value range constraints.
   * Defines minimum and maximum allowed values for input.
   */
  range?: { min?: number; max?: number }
  
  /**
   * Step increment for number input controls.
   */
  step?: number
  
  /**
   * Unit of measurement displayed next to the input field.
   * Example: '$' for currency, 'kg' for weight, etc.
   */
  unit?: string
}

export interface DateFilterItem extends BaseFilterItem {
  /**
   * Type of filter to render and validation to apply.
   * 'date': Date-based filtering with date range support
   */
  variant: 'date'
  
  /**
   * Date range constraints in ISO 8601 format.
   * Defines minimum and maximum allowed dates for input.
   * 
   * @see https://en.wikipedia.org/wiki/ISO_8601
   */
  range?: { min?: Date | string; max?: Date | string }
}

export interface BooleanFilterItem extends BaseFilterItem {
  /**
   * Type of filter to render and validation to apply.
   * 'boolean': True/false filtering
   */
  variant: 'boolean'
}

export interface SelectFilterItem<T = unknown> extends BaseFilterItem {
  /**
   * Type of filter to render and validation to apply.
   * 'select': Single selection from predefined options
   * 'multiSelect': Multiple selection from predefined options
   */
  variant: 'select' | 'multiSelect'
  
  /**
   * Available options for selection.
   */
  options: Array<{
    /**
     * Display text shown to users for this option.
     */
    label: string
    
    /**
     * Actual value used for filtering operations.
     */
    value: T
    
    /**
     * Optional count of items associated with this option.
     * Displayed next to the option label in the interface.
     */
    count?: number
    
    /**
     * Optional React component rendered as an icon next to the option.
     * Component must accept a className prop.
     */
    icon?: React.ComponentType<{ className?: string; [key: string]: any }>
  }>
}

export type FilterItem<T extends DynamicObject = DynamicObject> = 
  | TextFilterItem
  | NumberFilterItem
  | DateFilterItem
  | BooleanFilterItem
  | SelectFilterItem<T[keyof T]>

export interface FilterState {
  filterId: string
  id: string
  operator: string
  value: unknown
}

export interface FilterProps<T extends DynamicObject = DynamicObject> {
  /**
   * Array of filter item definitions that determine available filters.
   */
  items: FilterItem<T>[]
  
  /**
   * If true, updates URL using shallow routing without page reload.
   * @default false
   */
  shallow?: boolean
  
  /**
   * Debounce delay in milliseconds before applying filter and updating URL.
   * Used to reduce update frequency during user interaction.
   * @default 300
   */
  debounceMs?: number
  
  /**
   * CSS class applied to the filter trigger button only.
   */
  className?: string
}

// Search types
export interface SearchItem<T extends DynamicObject = DynamicObject> {
  id: Extract<keyof T, string>
  placeholder?: string
}

export interface SearchProps<T extends DynamicObject = DynamicObject> {
  item: SearchItem<T>
  shallow?: boolean
  debounceMs?: number
  className?: string
}

// Sorting types
export interface SortingItem<T extends DynamicObject = DynamicObject> {
  id: Extract<keyof T, string>
  label: string
}

export interface SortingState {
  id: string
  value: SortDirection
}

export interface SortingProps<T extends DynamicObject = DynamicObject> {
  items: SortingItem<T>[]
  className?: string
}

// Context types
export interface ToolbarContextValue {
  startTransition?: React.TransitionStartFunction
  dictionaries: ToolbarDictionaries
  dir: Direction
}

// Dictionary types
export interface ToolbarDictionaries {
  filter: FilterDictionary
  sorting: SortingDictionary
}

export interface FilterDictionary {
  triggerButtonLabel: string
  popover: {
    title: {
      noFilters: string
      withFilters: string
    }
    description: {
      noFilters: string
      withFilters: string
    }
    buttonLabels: {
      addFilter: string
      resetFilters: string
    }
  }
  filterItem: {
    JoinSelector: {
      where: string
      AND: string
      OR: string
    }
    itemSelector: {
      searchPlaceholder: string
      noFieldsFound: string
    }
    operatorSelector: {
      searchPlaceholder: string
      noOperatorsFound: string
      operators: {
        text: Record<TextOperator, string>
        number: Record<NumberOperator, string>
        date: Record<DateOperator, string>
        boolean: Record<BooleanOperator, string>
        select: Record<SelectOperator, string>
        multiSelect: Record<MultiSelectOperator, string>
      }
    }
    valueFilter: {
      searchPlaceholder: string
      noValuesFound: string
    }
  }
}

export interface SortingDictionary {
  triggerButtonLabel: string
  popover: {
    title: {
      noSorting: string
      withSorting: string
    }
    description: string
    buttonLabels: {
      addSorting: string
      resetSorting: string
    }
  }
  sortingItem: {
    fieldSelector: {
      searchPlaceholder: string
      noFieldsFound: string
    }
    directionSelector: {
      asc: string
      desc: string
    }
  }
}


