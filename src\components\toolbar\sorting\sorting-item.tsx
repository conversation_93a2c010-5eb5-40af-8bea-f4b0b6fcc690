import * as React from "react"
import { ChevronsUpDown, GripVertical, Trash2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { SortableItem, SortableItemHandle } from "@/components/ui/sortable"
import { useToolbar } from "../providers/toolbar-provider"
import { sortingOrders } from "../utils/sorting-lib"
import { SortDirection, SortingItemProps } from "../types/sorting"

function SortingItem({
  sort,
  items,
  sortItemId,
  onSortUpdate,
  onSortRemove,
}: SortingItemProps) {
  const { dictionaries, dir } = useToolbar()
  const fieldListboxId = `${sortItemId}-field-listbox`
  const fieldTriggerId = `${sortItemId}-field-trigger`
  const directionListboxId = `${sortItemId}-direction-listbox`
  const dict = dictionaries.sorting.sortingItem

  const [showFieldSelector, setShowFieldSelector] = React.useState(false)
  const [showDirectionSelector, setShowDirectionSelector] =
    React.useState(false)

  return (
    <SortableItem value={sort.id} asChild>
      <div
        role="listitem"
        id={sortItemId}
        tabIndex={-1}
        className="flex items-center gap-2"
      >
        <Popover open={showFieldSelector} onOpenChange={setShowFieldSelector}>
          <PopoverTrigger asChild>
            <Button
              id={fieldTriggerId}
              role="combobox"
              aria-controls={fieldListboxId}
              variant="outline"
              size="sm"
              className="w-44 justify-between rounded font-normal"
            >
              <span className="truncate">
                {items.find((item) => item.id === sort.id)?.label}
              </span>
              <ChevronsUpDown className="opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            dir={dir}
            id={fieldListboxId}
            className="w-[var(--radix-popover-trigger-width)] origin-[var(--radix-popover-content-transform-origin)] p-0"
          >
            <Command dir={dir}>
              <CommandInput
                placeholder={dict.fieldSelector.searchPlaceholder}
              />
              <CommandList>
                <CommandEmpty>{dict.fieldSelector.noFieldsFound}</CommandEmpty>
                <CommandGroup>
                  {items.map((column) => (
                    <CommandItem
                      key={column.id}
                      value={column.id}
                      onSelect={(value) =>
                        onSortUpdate(sort.id, {
                          id: value,
                        })
                      }
                    >
                      <span className="truncate">{column.label}</span>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <Select
          dir={dir}
          open={showDirectionSelector}
          onOpenChange={setShowDirectionSelector}
          value={sort.value}
          onValueChange={(value: _SortDirection) =>
            onSortUpdate(sort.id, { value: value })
          }
        >
          <SelectTrigger
            aria-controls={directionListboxId}
            className="h-8 w-24 rounded [&[data-size]]:h-8"
          >
            <SelectValue />
          </SelectTrigger>
          <SelectContent
            id={directionListboxId}
            className="min-w-[var(--radix-select-trigger-width)] origin-[var(--radix-select-content-transform-origin)]"
          >
            {sortingOrders.map((order: SortDirection) => (
              <SelectItem key={order} value={order}>
                {dict.directionSelector[order]}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button
          aria-controls={sortItemId}
          variant="outline"
          size="icon"
          className="size-8 shrink-0 rounded"
          onClick={() => onSortRemove(sort.id)}
        >
          <Trash2 />
        </Button>
        <SortableItemHandle asChild>
          <Button
            variant="outline"
            size="icon"
            className="size-8 shrink-0 rounded"
          >
            <GripVertical />
          </Button>
        </SortableItemHandle>
      </div>
    </SortableItem>
  )
}

export {
  SortingItem,
  // Legacy export for backward compatibility during transition
  SortingItem as _SortingItem
}
