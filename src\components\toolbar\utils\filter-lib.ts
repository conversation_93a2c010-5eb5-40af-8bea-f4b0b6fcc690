import { z } from "zod/v4"

import { FilterVariant } from "../types"
import { GetToolbarDictionaryResult } from "./toolbar-lib"

const textOperators = [
  "iLike",
  "notILike",
  "eq",
  "ne",
  "isEmpty",
  "isNotEmpty",
] as const
const numericOperators = [
  "eq",
  "ne",
  "lt",
  "lte",
  "gt",
  "gte",
  "isBetween",
  "isEmpty",
  "isNotEmpty",
] as const
const dateOperators = [
  "eq",
  "ne",
  "lt",
  "lte",
  "gt",
  "gte",
  "isBetween",
  "isEmpty",
  "isNotEmpty",
] as const
const multiSelectOperators = [
  "inArray",
  "notInArray",
  "isEmpty",
  "isNotEmpty",
] as const
const selectOperators = ["eq", "ne", "isEmpty", "isNotEmpty"] as const
const booleanOperators = ["eq", "ne"] as const

const getDefaultFilterOperator = (filterVariant: FilterVariant) => {
  switch (filterVariant) {
    case "text":
      return "iLike"
    case "number":
      return "eq"
    case "date":
      return "eq"
    case "boolean":
      return "eq"
    case "select":
      return "eq"
    case "multiSelect":
      return "inArray"
    default:
      return "eq"
  }
}

const getFilterOperators = (
  filterVariant: FilterVariant,
  dict: GetToolbarDictionaryResult["filter"]["filterItem"]["operatorSelector"]["operators"]
) => {
  switch (filterVariant) {
    case "text":
      return textOperators.map((o) => ({
        value: o,
        label: dict.text[o],
      }))
    case "number":
    case "numberRange":
      return numericOperators.map((o) => ({
        value: o,
        label: dict.number[o],
      }))
    case "date":
    case "dateRange":
      return dateOperators.map((o) => ({
        value: o,
        label: dict.date[o],
      }))
    case "boolean":
      return booleanOperators.map((o) => ({
        value: o,
        label: dict.boolean[o],
      }))
    case "select":
      return selectOperators.map((o) => ({
        value: o,
        label: dict.select[o],
      }))
    case "multiSelect":
      return multiSelectOperators.map((o) => ({
        value: o,
        label: dict.multiSelect[o],
      }))
  }
}

const filterTextStateSchema = z.object({
  id: z.string(),
  value: z.string().min(1),
  variant: z.enum(["text"]), // text
  operator: z.enum(textOperators),
  filterId: z.string(),
})

const filterNumericStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["number"]), // number
  operator: z.enum(numericOperators).exclude(["isBetween"]),
  value: z.string().min(1),
})

const filterNumericRangeStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["numberRange"]), // numberRange
  operator: z.enum(["isBetween"]),
  value: z.array(z.string()).min(2),
})

const filterDateStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["date"]), // date
  operator: z.enum(dateOperators).exclude(["isBetween"]),
  value: z.string().min(1),
})

const filterDateRangeStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["dateRange"]), // dateRange
  operator: z.enum(["isBetween"]),
  value: z.array(z.string()).min(2),
})

const filterBooleanStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["boolean"]), // boolean
  operator: z.enum(booleanOperators),
  value: z.stringbool(),
})

const filterSelectStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["select"]), // select
  operator: z.enum(selectOperators),
  value: z.string().min(1),
})

const filterMultiSelectStateSchema = filterTextStateSchema.extend({
  variant: z.enum(["multiSelect"]), // multiSelect
  operator: z.enum(multiSelectOperators),
  value: z.union([z.array(z.string()).min(1), z.enum(["empty", "notEmpty"])]),
})

const filterJoinStateSchema = z.enum(["AND", "OR"])

const filterStateSchema = z.discriminatedUnion("variant", [
  filterTextStateSchema,
  filterNumericStateSchema,
  filterNumericRangeStateSchema,
  filterDateStateSchema,
  filterDateRangeStateSchema,
  filterBooleanStateSchema,
  filterSelectStateSchema,
  filterMultiSelectStateSchema,
])

type FilterState = z.infer<typeof filterStateSchema>
type FilterJoinState = z.infer<typeof filterJoinStateSchema>

export {
  filterStateSchema,
  type FilterState,
  type FilterJoinState,
  getDefaultFilterOperator,
  getFilterOperators,
  // Legacy exports for backward compatibility during transition
  getDefaultFilterOperator as _getDefaultFilterOperator,
  getFilterOperators as _getFilterOperators,
  filterStateSchema as _filterStateSchema,
  type FilterState as _FilterState,
  type FilterJoinState as _FilterJoinState,
}
