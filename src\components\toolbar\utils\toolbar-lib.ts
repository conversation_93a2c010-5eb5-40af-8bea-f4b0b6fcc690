import React from "react"
import {
  createSearchParamsCache,
  parseAsStringEnum,
  SearchParams,
} from "nuqs/server"

import { Locale } from "@/types/globals"

import toolbarDictionariesAr from "../locales/ar"
import toolbarDictionariesEn from "../locales/en"
import {
  getFilterQueryParser,
  getFiltersStateParser,
} from "./filter-parser"
import {
  getSearchStateParser,
  searchQueryParser,
} from "./search-parser"
import {
  getSortingQueryParser,
  getSortingStateParser,
} from "./sorting-lib"

type GetToolbarDictionaryResult = ReturnType<typeof getToolbarDictionary>

const toolbarDictionaries = {
  en: toolbarDictionariesEn,
  ar: toolbarDictionariesAr,
}

const getToolbarDictionary = (locale: Locale) => {
  return toolbarDictionaries[locale]
}

const getToolbarParser = React.cache(
  async (searchParams: Promise<SearchParams> | SearchParams) => {
    const searchParamsCache = createSearchParamsCache({
      filters: getFiltersStateParser().withDefault([]),
      join: parseAsStringEnum(["AND", "OR"]).withDefault("AND"),
      search: getSearchStateParser().withDefault({}),
      sort: getSortingStateParser().withDefault([]),
    })

    const searchParamsCacheParsed = searchParamsCache.parse(await searchParams)

    const filters = getFilterQueryParser({
      filters: searchParamsCacheParsed.filters,
      join: searchParamsCacheParsed.join,
    })

    const search = searchQueryParser(searchParamsCacheParsed.search)
    const sorting = getSortingQueryParser(searchParamsCacheParsed.sort)

    const where = { ...filters, ...search }
    const orderBy = sorting

    return { where, orderBy }
  }
)

export {
  getToolbarParser,
  getToolbarDictionary,
  type GetToolbarDictionaryResult,
}
