"use client"

import * as React from "react"
import dynamic from "next/dynamic"
import { ListFilter } from "lucide-react"
import { parseAsStringEnum, useQueryState } from "nuqs"

import { DynamicObject } from "@/types/globals"
import { generateId } from "@/lib/id"
import { cn } from "@/lib/utils"
import { useDebouncedCallback } from "@/hooks/use-debounced-callback"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Sortable, SortableContent } from "@/components/ui/sortable"

import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover"
import { Skeleton } from "../../ui/skeleton"
import {
  FilterContextValue,
  FilterProvider,
} from "../providers/filter-provider"
import { useToolbar } from "../providers/toolbar-provider"
import { FilterProps } from "../types/filter"
import { FilterState, getDefaultFilterOperator } from "../utils/filter-lib"
import { getFiltersStateParser } from "../utils/filter-parser"

// Dynamically import FilterItem component for better initial page load performance
const FilterItem = dynamic(
  () => import("./filter-item").then((e) => e.FilterItem),
  {
    ssr: false,
    loading: () => <FilterListItemSkeleton />,
  }
)

export function Filter<T extends DynamicObject = DynamicObject>({
  items,
  shallow = false,
  debounceMs = 300,
  className,
}: FilterProps<T>) {
  const id = React.useId()
  const labelId = React.useId()
  const descriptionId = React.useId()
  const { startTransition } = useToolbar()
  const { dictionaries, dir } = useToolbar()
  const dict = dictionaries.filter

  const [filters, setFilters] = useQueryState(
    "filters",
    getFiltersStateParser(items.map((item) => item.id))
      .withDefault([])
      .withOptions({
        startTransition,
        clearOnDefault: true,
        throttleMs: debounceMs,
        shallow,
      })
  )

  const [joinOperator, setJoinOperator] = useQueryState(
    "join",
    parseAsStringEnum(["AND", "OR"]).withDefault("AND").withOptions({
      startTransition,
      clearOnDefault: true,
      shallow: false,
    })
  )

  const onFilterAdd = React.useCallback(() => {
    const item = items[0]
    if (!item) return
    setFilters((prev) => [
      ...prev,
      {
        id: item.id,
        value: "",
        variant: item.variant,
        operator: getDefaultFilterOperator(item.variant),
        filterId: generateId({ length: 8 }),
      } as FilterState,
    ])
  }, [items, setFilters])

  const debouncedSetFilters = useDebouncedCallback(setFilters, debounceMs)

  const onFilterUpdate: FilterContextValue["onFilterUpdate"] =
    React.useCallback(
      (filterId, updates) => {
        debouncedSetFilters(
          (prevFilters) =>
            prevFilters.map((filter) => {
              if (filter.filterId === filterId) {
                return { ...filter, ...updates }
              }
              return filter
            }) as FilterState[]
        )
      },
      [debouncedSetFilters]
    )

  const onFilterRemove: FilterContextValue["onFilterRemove"] =
    React.useCallback(
      (filterId) => {
        setFilters((prevFilters) =>
          prevFilters.filter((filter) => filter.filterId !== filterId)
        )
      },
      [setFilters]
    )

  const onFiltersReset = React.useCallback(() => {
    setJoinOperator("AND")
    setFilters([])
  }, [setFilters, setJoinOperator])

  return (
    <FilterProvider
      value={{
        items,
        filters,
        joinOperator,
        setJoinOperator,
        onFilterUpdate,
        onFilterRemove,
        onFiltersReset,
        onFilterAdd,
      }}
    >
      <Sortable
        value={filters}
        onValueChange={setFilters}
        getItemValue={(item: FilterState) => item.filterId}
      >
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className={className}>
              <ListFilter />
              {dict.triggerButtonLabel}
              {filters.length > 0 && (
                <Badge
                  variant="secondary"
                  className="h-[18.24px] rounded-[3.2px] px-[5.12px] font-mono text-[10.4px] font-normal"
                >
                  {filters.length}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            dir={dir}
            align="start"
            aria-describedby={descriptionId}
            aria-labelledby={labelId}
            className="flex w-full max-w-[var(--radix-popover-content-available-width)] origin-[var(--radix-popover-content-transform-origin)] flex-col gap-3.5 p-4 sm:min-w-[380px]"
          >
            <div className="flex flex-col gap-1">
              <h4 id={labelId} className="leading-none font-medium">
                {filters.length > 0
                  ? dict.popover.title.withFilters
                  : dict.popover.title.noFilters}
              </h4>
              <p
                id={descriptionId}
                className={cn(
                  "text-muted-foreground text-sm",
                  filters.length > 0 && "sr-only"
                )}
              >
                {filters.length > 0
                  ? dict.popover.description.withFilters
                  : dict.popover.description.noFilters}
              </p>
            </div>
            {filters.length > 0 ? (
              <SortableContent dir={dir} asChild>
                <div
                  role="list"
                  className="flex max-h-[300px] flex-col gap-2 overflow-y-auto p-1"
                >
                  {filters.length > 0 &&
                    filters.map((filter, index) => (
                      <FilterItem
                        index={index}
                        key={filter.filterId}
                        filter={filter as FilterState}
                        filterItemId={`${id}-filter-${filter.filterId}`}
                      />
                    ))}
                </div>
              </SortableContent>
            ) : null}
            <div className="flex w-full items-center gap-2">
              <Button size="sm" className="rounded" onClick={onFilterAdd}>
                {dict.popover.buttonLabels.addFilter}
              </Button>
              {filters.length > 0 ? (
                <Button
                  variant="outline"
                  size="sm"
                  className="rounded"
                  onClick={onFiltersReset}
                >
                  {dict.popover.buttonLabels.resetFilters}
                </Button>
              ) : null}
            </div>
          </PopoverContent>
        </Popover>
      </Sortable>
    </FilterProvider>
  )
}

function FilterListItemSkeleton() {
  return (
    <div className="flex items-center gap-2">
      <Skeleton className="h-8 min-w-[72px] rounded" />
      <Skeleton className="h-8 w-32 rounded" />
      <Skeleton className="h-8 w-32 rounded" />
      <Skeleton className="h-8 min-w-36 flex-1 rounded" />
      <Skeleton className="size-8 shrink-0 rounded" />
      <Skeleton className="size-8 shrink-0 rounded" />
    </div>
  )
}
