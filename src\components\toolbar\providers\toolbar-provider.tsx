"use client"

import React from "react"

import { ToolbarProps } from "../toolbar-main"
import { GetToolbarDictionaryResult } from "../utils/toolbar-lib"

type ToolbarContextValue = Omit<ToolbarProps, "children" | "className"> & {
  dictionaries: GetToolbarDictionaryResult
}

const ToolbarContext = React.createContext<ToolbarContextValue | null>(null)

function ToolbarProvider({
  value,
  children,
}: {
  value: ToolbarContextValue
  children: React.ReactNode
}) {
  return <ToolbarContext value={value}>{children}</ToolbarContext>
}

export function useToolbar() {
  const context = React.useContext(ToolbarContext)
  if (!context) {
    throw new Error("useToolbarContext must be used within a ToolbarProvider")
  }
  return context
}

export { ToolbarProvider }
