import { z } from "zod"

const searchStateSchema = z.union([
  z.object({
    id: z.string().min(1),
    value: z.string().min(1),
  }),
  z.object({
    id: z.undefined(),
    value: z.undefined(),
  }),
])

type SearchState = z.infer<typeof searchStateSchema>

export {
  searchStateSchema,
  type SearchState,
  // Legacy exports for backward compatibility during transition
  searchStateSchema as _searchStateSchema,
  type SearchState as _SearchState,
}
