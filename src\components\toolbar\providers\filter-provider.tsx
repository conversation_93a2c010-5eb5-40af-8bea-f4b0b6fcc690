import * as React from "react"

import { FilterJoinState, FilterState } from "../utils/filter-lib"
import { FilterItemDef } from "../types/filter"

export interface FilterContextValue {
  items: FilterItemDef[]
  filters: FilterState[]
  joinOperator: FilterJoinState
  setJoinOperator: (value: FilterJoinState) => void
  onFilterRemove: (filterId: string) => void
  onFiltersReset: () => void
  onFilterAdd: () => void
  onFilterUpdate: (
    filterId: string,
    updates: Partial<Omit<FilterState, "filterId">>
  ) => void
}

const FilterContext = React.createContext<FilterContextValue | null>(null)

export function FilterProvider({
  value,
  children,
}: {
  value: FilterContextValue
  children: React.ReactNode
}) {
  return <FilterContext value={value}>{children}</FilterContext>
}

export function useFilter() {
  const context = React.useContext(FilterContext)
  if (!context) {
    throw new Error("useFilterContext must be used within a FilterProvider")
  }
  return context
}

// Legacy exports for backward compatibility during transition
export type { FilterContextValue as _FilterContextValue }
export { FilterProvider as _FilterProvider }
